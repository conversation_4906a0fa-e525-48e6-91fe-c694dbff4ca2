@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fonts are now loaded optimally in _app.js - removed duplicate import */

html,
body {
  *,
  ::before,
  ::after {
    border-width: 0;
    border-style: solid;
    border-color: theme("borderColor.DEFAULT", currentColor);
  }

  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  margin: 0;
  font-family: "Inter", sans-serif;
  color: #333;
  background-color: theme("colors.secondary-dark");

  &:before {
    content: "";
    width: 100%;
    height: 100vh;
    background-color: theme("colors.secondary-dark");
    position: fixed;
    left: 0;
    top: 0;
    z-index: -1;
  }
}

/* Base styles */
@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-semibold;
  }

  h1 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  p {
    @apply leading-relaxed;
  }

  a {
    @apply text-primary hover:text-primary-dark transition-colors duration-200;
  }
}

.text-shadow {
  text-shadow: 0px 2px 0px rgb(0 0 0 / 30%);
}

.adjacent-post {
  & .arrow-btn {
    transition: width 300ms ease;
    width: 50px;
  }
  &:hover {
    & .arrow-btn {
      width: 60px;
    }
  }
}

.react-multi-carousel-list {
  & .arrow-btn {
    transition: width 300ms ease;
    width: 50px;
    &:hover {
      width: 60px;
    }
  }
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Keyframes for smooth animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent layout shift during loading */
.loading-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
